{"auth": {"login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out"}, "header": {"logo": "ResourceHub", "navigation": {"home": "Home", "platforms": "Platforms", "pricing": "Pricing", "categories": "Categories", "features": "Features", "faq": "FAQ"}}, "hero": {"title": "Discover Millions of", "titleHighlight": "Creative Resources", "description": "Download high-quality stock photos, vectors, illustrations, and videos. Get credits to access premium content from top creative platforms worldwide.", "searchPlaceholder": "Search for photos, vectors, illustrations...", "searchPlaceholderMobile": "Search photos, vectors...", "searchButton": "Search", "popularSearches": "Popular searches :", "viewPricing": "View Pricing", "contactUs": "Contact Us", "searchType": {"placeholder": "All Content", "all": "All Content", "images": "Images", "vectors": "Vectors", "videos": "Videos", "templates": "Templates", "icons": "Icons"}, "imageSearch": {"buttonText": "Search by Image", "dialogTitle": "Search by Image", "dialogDescription": "Upload an image to find similar content across our platforms", "dragAndDrop": "Drag and drop your image here", "orClickToBrowse": "or click to browse files", "tipText": "Your image must be a JPG or PNG and be smaller than 5MB", "uploading": "Uploading image...", "uploadSuccess": "Image uploaded successfully!", "uploadError": "Failed to upload image. Please try again.", "supportedFormats": "Supported formats: JPG, PNG (max 5MB)"}}, "features": {"title": "Why Choose", "titleHighlight": "Resource Hub", "description": "Everything you need to access premium creative resources from top platforms worldwide.", "premiumQuality": {"title": "Premium Quality", "description": "Access millions of high-resolution images, vectors, and videos from top creative platforms with guaranteed quality."}, "instantDownloads": {"title": "Instant Downloads", "description": "Download your favorite resources instantly with our optimized servers and lightning-fast delivery system."}, "allPlatforms": {"title": "All Platforms", "description": "One subscription gives you access to Freepik, Shutterstock, Adobe Stock, and many more premium platforms."}, "secureAndSafe": {"title": "Secure & Safe", "description": "Your data is protected with enterprise-grade security. Safe downloads with no malware or unwanted software."}, "support24": {"title": "24/7 Support", "description": "Get help whenever you need it with our dedicated support team available around the clock."}, "easyToUse": {"title": "Easy to Use", "description": "Simple and intuitive interface designed for creators. Search, preview, and download in just a few clicks."}}, "supportedPlatforms": {"title": "Trusted by Millions", "titleHighlight": "Worldwide", "description": "Access premium content from the world's leading creative platforms. One subscription, unlimited possibilities.", "platforms": {"freepik": "Freepik", "shutterstock": "Shutterstock", "nexus": "Nexus", "raw": "Raw Pixel"}}, "categories": {"title": "Popular", "titleHighlight": "Categories", "description": "Discover trending content across our most popular categories and find exactly what you're looking for.", "items": {"nature": "Nature", "business": "Business", "technology": "Technology", "travel": "Travel", "sports": "Sports", "art": "Art"}}, "pricing": {"title": "Choose Your", "titleHighlight": "Perfect Plan", "description": "Get access to millions of premium resources with our flexible pricing plans designed for every need.", "basic": {"title": "Basic Plan", "subtitle": "Perfect for getting started", "price": "Contact Us", "priceSubtitle": "Custom pricing available", "credits": "50", "validity": "15 days", "supportedSites": "2 sites", "features": ["Access to all sites", "24/7 Support", "Admin Management"], "button": "Contact Us"}, "advanced": {"title": "Advanced Plan", "subtitle": "Perfect for management", "price": "$99", "priceSubtitle": "Best value for professionals", "credits": "1,000", "validity": "30 days", "supportedSites": "3 sites", "features": ["Everything in Basic", "Priority Support", "Advanced Analytics", "Team Management"], "button": "Get Started"}, "premium": {"title": "Premium Plan", "subtitle": "Perfect for teams", "price": "$199", "priceSubtitle": "Ultimate creative freedom", "credits": "5,000", "validity": "30 days", "supportedSites": "5 sites", "features": ["Everything in Advanced", "Unlimited Downloads", "Custom Integrations", "Dedicated Account Manager"], "button": "Get Started"}, "labels": {"credits": "Credits:", "validity": "Validity:", "supportedSites": "Supported Sites:", "featuresIncluded": "Features included:", "supportedSitesDialog": {"title": "Supported Sites", "description": "These are the websites you can access with this plan"}}, "features": {"main": "All plans include access to our premium resource library and customer support.", "moneyBack": "30-day money-back guarantee", "support24": "24/7 Support", "cancelAnytime": "Cancel anytime"}}, "faq": {"title": "Frequently Asked", "titleHighlight": "Questions", "description": "Find answers to the most common questions about our platform and services.", "questions": {"whatIsResourceHub": {"question": "What is Resource Hub and how does it work?", "answer": "Resource Hub is a premium platform that provides access to millions of high-quality creative resources from top platforms like Freepik, Shutterstock, Adobe Stock, and more. We offer a credit-based system where you can download premium content using credits from your subscription plan."}, "howCreditsWork": {"question": "How do credits work and what can I download with them?", "answer": "Credits are used to download premium resources from supported platforms. Each download typically costs 1 credit, regardless of the file type (images, vectors, videos, or templates). Your credits are valid for the duration of your subscription period and can be used across all supported platforms."}, "supportedPlatforms": {"question": "Which creative platforms are supported?", "answer": "We support major creative platforms including Freepik, Shutterstock, Adobe Stock, Getty Images, Unsplash, and many more. The number of supported platforms varies by subscription plan, with premium plans offering access to more platforms and exclusive content."}, "howToDownload": {"question": "How do I download resources from the platform?", "answer": "Simply search for the content you need, select the resource you want, and click download. The system will automatically use one credit from your account and provide you with the high-quality file. All downloads are instant and available in multiple formats when applicable."}, "safetyLegal": {"question": "Is it safe and legal to use these resources?", "answer": "Yes, absolutely! All resources available through our platform are legally licensed and safe to use for commercial and personal projects. We maintain partnerships with content creators and platforms to ensure all downloads are legitimate and properly licensed."}}}, "footer": {"description": "Your ultimate destination for premium creative resources. Access millions of high-quality images, vectors, and videos from top platforms worldwide.", "quickLinks": "Quick Links", "resources": "Resources", "support": "Support", "legal": "Legal", "followUs": "Follow Us", "allRightsReserved": "All rights reserved.", "links": {"home": "Home", "categories": "Categories", "pricing": "Pricing", "about": "About", "helpCenter": "Help Center", "contactSupport": "Contact Support", "faq": "FAQ", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "cookiePolicy": "<PERSON><PERSON>", "stockPhotos": "Stock Photos", "vectors": "Vectors", "illustrations": "Illustrations", "videos": "Videos", "templates": "Templates"}}, "dashboard": {"title": "Dashboard", "stats": {"totalUsers": {"title": "Total Users", "description": "Registered accounts"}, "activeSites": {"title": "Active Sites", "description": "Live websites"}, "onlineUsers": {"title": "Online Users", "description": "Currently active"}, "totalCreditsIssued": "Total Credits Issued", "totalCreditsUsed": "Total Credits Used", "remainingCredits": "Remaining Credits", "registeredUsers": "Registered users in the system", "onlineNow": "online now", "systemWideCreditsDistributed": "System-wide credits distributed", "creditsConsumedByUsers": "Credits consumed by users", "availableCreditsInSystem": "Available credits in system", "totalDownloads": {"title": "Total Downloads", "description": "All time downloads"}, "creditsUsed": {"title": "Credits Used", "description": "Total consumed"}, "creditsRemaining": {"title": "Credits Remaining", "description": "Available balance"}, "planStatus": {"title": "Plan Status", "description": "Current subscription"}}, "usersManagement": {"title": "Users Management", "description": "Manage user subscriptions and monitor activity", "searchPlaceholder": "Search users by name, email, phone...", "filters": {"allStatus": "All Status", "active": "Active", "expired": "Expired", "suspended": "Suspended"}, "table": {"headers": {"user": "USER", "phone": "PHONE", "planCredits": "PLAN & CREDITS", "status": "STATUS", "expiry": "EXPIRY", "actions": "ACTIONS"}, "loadingUsers": "Loading users...", "loadingDescription": "Please wait while we fetch the user data", "credits": "credits", "deleteUser": "Delete User", "noUsersFound": "No users found", "noUsersDescription": "Try adjusting your search or filter criteria", "showingUsers": "Showing {{filtered}} of {{total}} users", "lastActive": "Last active: {{time}}", "joined": "Joined", "expires": "Expires", "errorLoading": "Error fetching users"}}, "addSubscription": {"title": "Add New Subscription", "description": "Create a subscription for a user", "userEmail": "User Email", "plan": "Subscription Plan", "subscriptionPlan": "Subscription Plan", "planPreview": "Plan Preview", "planName": "Plan Name :", "credits": "credits", "adding": "Adding...", "add": "Add Subscription", "addingSubscription": "Adding Subscription...", "addSubscription": "Add Subscription", "choosePlan": "Choose a subscription plan", "emailPlaceholder": "<EMAIL>", "placeholders": {"email": "<EMAIL>", "plan": "Choose a subscription plan"}, "validation": {"emailRequired": "Email is required", "invalidEmail": "Please enter a valid email address", "planRequired": "Please select a subscription plan"}}, "upgradeSubscription": {"title": "Upgrade Subscription", "description": "Upgrade user to a higher plan", "userEmail": "User Email", "newPlan": "New Plan", "credits": "credits", "upgrading": "Upgrading...", "upgrade": "Upgrade", "placeholders": {"email": "<EMAIL>", "plan": "Choose a new plan"}, "validation": {"emailRequired": "Email is required", "invalidEmail": "Please enter a valid email address", "planRequired": "Please select a new plan"}}, "extendSubscription": {"title": "Extend Subscription", "description": "Extend user subscription validity", "userEmail": "User Email", "days": "Additional Days", "additionalDays": "Additional Days", "extending": "Extending...", "extend": "Extend", "estimatedNewExpiry": "Estimated New Expiry", "placeholders": {"email": "<EMAIL>", "days": "30"}, "validation": {"emailRequired": "Email is required", "invalidEmail": "Please enter a valid email address", "daysRequired": "Please enter a valid number of days"}}, "deleteSubscription": {"title": "Delete Subscription", "description": "Remove user subscription", "warning": "Warning: This action is irreversible", "warningDescription": "All subscription data and credits associated with this user will be permanently deleted.", "userEmail": "User Email", "deleting": "Deleting...", "delete": "Delete Subscription", "placeholders": {"email": "<EMAIL>"}, "validation": {"emailRequired": "Email is required", "invalidEmail": "Please enter a valid email address"}}, "siteManagement": {"title": "Site Management", "description": "Manage supported websites", "addSite": "Add New Site", "siteName": "Site Name", "siteUrl": "Site URL", "price": "Price", "siteIcon": "Site Icon URL (Optional)", "adding": "Adding Site...", "add": "Add Site", "status": "Status", "addedDate": "Added Date", "table": {"headers": {"site": "Site", "url": "URL", "price": "Price", "status": "Status", "actions": "Actions"}, "noSites": "No sites found", "noSitesDescription": "Add your first site to get started", "accessPrice": "Access Price", "credits": "credits", "active": "Active", "delete": "Delete", "retry": "Retry"}, "placeholders": {"siteName": "Enter site name", "siteUrl": "https://example.com", "sitePrice": "Enter price in credits", "siteIcon": "https://example.com/icon.png", "siteIconHelp": "Optional - we'll use a default icon if not provided"}, "validation": {"nameRequired": "Site name is required", "urlRequired": "Site URL is required", "invalidUrl": "Please enter a valid URL", "priceRequired": "Price is required", "invalidPrice": "Please enter a valid price"}, "toast": {"addSuccess": "Website added successfully", "addError": "Failed to add website", "deleteSuccess": "Website deleted successfully", "deleteError": "Failed to delete website"}}, "packageManagement": {"title": "Pricing Management", "description": "Manage subscription plans and pricing", "addPackage": "Add New Plan", "editPackage": "Edit Plan", "packageName": "Plan Name", "packagePrice": "Plan Price (Optional - leave empty to show 'Contact Us')", "packageDescription": "Plan Description", "daysValidity": "Validity Period (in days)", "credits": "Number of Credits", "contactUrl": "Contact Us URL", "supportedSites": "Supported Sites (comma-separated)", "adding": "Adding Plan...", "updating": "Updating Plan...", "deleting": "Deleting Plan...", "add": "Add Plan", "update": "Update Plan", "delete": "Delete Plan", "edit": "Edit", "confirmDelete": "Are you sure you want to delete this plan?", "deleteDescription": "This action cannot be undone. This will permanently delete the plan and may affect users subscribed to it.", "noPackages": "No plans available", "noPackagesDescription": "Start by adding a new plan to manage subscriptions", "noPricingPlans": "No pricing plans available", "editDescription": "Edit plan details", "planDetails": {"name": "Plan Name:", "price": "Price:", "description": "Description:", "validity": "Validity Period:", "credits": "Credits:", "contactUs": "Contact Us:", "supportedSites": "Supported Sites:", "days": "days", "contactUsLabel": "Contact Us"}, "placeholders": {"packageName": "Enter plan name", "packageDescription": "Enter plan description", "packagePrice": "Leave empty to show 'Contact Us'", "packagePriceHelp": "Leave empty to display Contact Us instead of a price", "daysValidity": "30", "credits": "100", "contactUrl": "https://example.com/contact", "supportedSites": "freepik, shutterstock, adobe", "supportedSitesHelp": "Enter comma-separated list of site names or domains (must match existing sites)"}, "buttons": {"cancel": "Cancel", "adding": "Adding...", "updating": "Updating...", "addPackage": "Add Package", "updatePackage": "Update Package"}, "validation": {"nameRequired": "Plan name is required", "descriptionRequired": "Description is required", "daysRequired": "Validity period is required", "invalidDays": "Please enter a valid number of days", "creditsRequired": "Number of credits is required", "invalidCredits": "Please enter a valid number of credits", "sitesRequired": "At least one supported site is required", "contactUrlRequired": "Contact URL is required", "invalidUrl": "Please enter a valid URL"}, "toast": {"addSuccess": "Pricing plan added successfully", "addError": "Failed to add pricing plan", "updateSuccess": "Pricing plan updated successfully", "updateError": "Failed to update pricing plan", "deleteSuccess": "Pricing plan deleted successfully", "deleteError": "Failed to delete pricing plan"}, "features": {"accessToSites": "Access to supported sites", "support": "24/7 Support", "adminManagement": "Admin Management"}}, "creditAnalytics": {"title": "Credit Analytics", "description": "System-wide credit usage statistics", "totalIssued": "Total Issued", "totalUsed": "Total Used", "remaining": "Remaining", "used": "Used", "dailyAverage": "Daily Average", "dailyAvg": "Daily Avg", "creditsByPlan": "Credits by Plan", "lastUpdated": "Last updated", "noAnalyticsData": "No analytics data available", "retry": "Retry", "refresh": "Refresh"}, "creditHistory": {"title": "Credit History", "description": "Recent credit transactions", "noHistoryData": "No credit history available", "retry": "Retry", "refresh": "Refresh", "showAll": "Show All", "total": "total", "transactions": {"siteAccess": "Site access", "creditPurchase": "Credit Purchase", "planUpgrade": "Plan Upgrade", "timeAgo": {"now": "now", "minutesAgo": "{{count}}m ago", "minutesAgo_one": "{{count}} minute ago", "minutesAgo_other": "{{count}} minutes ago", "hoursAgo": "{{count}}h ago", "hoursAgo_one": "{{count}} hour ago", "hoursAgo_other": "{{count}} hours ago", "daysAgo": "{{count}}d ago", "daysAgo_one": "{{count}} day ago", "daysAgo_other": "{{count}} days ago"}}}, "errors": {"failedToLoadAnalytics": "Failed to load analytics", "failedToLoadHistory": "Failed to load history", "failedToLoadUsers": "Failed to load users", "failedToLoadDownloads": "Failed to load downloads", "failedToLoadSites": "Failed to load sites", "failedToLoadPricingPlans": "Failed to load pricing plans", "failedToAddSubscription": "Failed to add subscription", "failedToUpgradeSubscription": "Failed to upgrade subscription", "failedToExtendSubscription": "Failed to extend subscription", "failedToDeleteSubscription": "Failed to delete subscription"}, "buttons": {"retry": "Retry"}}, "sidebar": {"sections": {"overview": "Overview", "management": "Management", "account": "Account"}, "navigation": {"dashboard": "Dashboard", "broadcast": "Broadcast", "usersManagement": "Users Management", "sitesManagement": "Sites Management", "pricingManagement": "Pricing Management", "cookiesManagement": "Cookies Management", "profile": "Profile", "settings": "Settings", "signOut": "Sign Out"}}, "profile": {"title": "Profile", "userInfo": {"member": "Member", "active": "Active", "editProfile": "Edit Profile", "creditsLeft": "Credits Left", "downloads": "Downloads", "memberSince": "Member Since", "onlineStatus": "Online"}, "stats": {"totalDownloads": {"title": "Total Downloads", "description": "All time downloads"}, "creditsUsed": {"title": "Credits Used", "description": "Total consumed"}, "creditsRemaining": {"title": "Credits Remaining", "description": "Available balance"}, "planStatus": {"title": "Plan Status", "description": "Current subscription"}}, "subscription": {"title": "Subscription Details", "description": "Your current plan information", "currentPlan": "Current Plan", "status": "Status", "validUntil": "<PERSON>id <PERSON>", "manageSubscription": "Manage Subscription"}, "credits": {"title": "Credits Overview", "description": "Your credit usage and balance", "remaining": "Credits Remaining", "usageProgress": "Usage Progress", "used": "Used", "total": "Total", "buyMore": "Buy More Credits"}, "changePassword": {"title": "Change Password", "description": "Update your account password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "currentPasswordPlaceholder": "Enter your current password", "newPasswordPlaceholder": "Enter your new password", "confirmPasswordPlaceholder": "Confirm your new password", "updatePassword": "Update Password", "passwordRequirements": "Password must be at least 8 characters long", "success": "Password updated successfully", "error": "Failed to update password"}, "downloadHistory": {"title": "Download History", "description": "Your recent downloads and credit usage", "filters": {"newest": "Newest First", "oldest": "Oldest First", "creditsHigh": "Credits: High to Low", "creditsLow": "Credits: Low to High"}, "empty": {"title": "No downloads found", "description": "Your download history will appear here"}, "loadMore": "Load More Downloads", "credits": "credits", "types": {"image": "image", "video": "video"}}}, "cookies": {"title": "Cookies Control", "description": "Manage your website cookies and user sessions.", "addCookie": {"title": "Add <PERSON>", "description": "Upload a new cookie file to manage website sessions", "button": "Add <PERSON>", "dialog": {"title": "Add New Cookie", "description": "Upload a cookie file to add to your collection", "dragAndDrop": {"title": "Drag and drop your cookie file here", "subtitle": "or click to browse files", "supportedFormats": "Supported formats: .txt, .json, .cookie"}, "uploading": "Uploading...", "upload": "Upload <PERSON><PERSON>"}}, "fields": {"username": "Username", "credit": "Credit", "lastUpdate": "Last Update", "status": "Status"}, "status": {"active": "Yes", "inactive": "No"}, "deleteDialog": {"title": "Delete Cookie", "description": "Are you sure you want to delete the cookie for {{domain}}? This action cannot be undone."}, "deleting": "Deleting..."}, "login": {"title": "Welcome Back", "subtitle": "Sign in to access your account", "form": {"email": {"label": "Email Address", "placeholder": "Enter your email address"}, "password": {"label": "Password", "placeholder": "Enter your password"}, "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "submitButton": "<PERSON><PERSON>", "submitting": "Signing in..."}, "validation": {"emailRequired": "Email address is required", "invalidEmail": "Please enter a valid email address", "passwordRequired": "Password is required"}, "footer": {"noAccount": "Don't have an account?", "registerLink": "Register now"}}, "terms": {"title": "Terms of Service", "lastUpdated": "Last updated: July 2025", "sections": {"cookies": {"title": "Web Cookies", "content": "If you leave a comment on our site, you may opt-in to saving your name, email address and website in cookies. These are for your convenience so that you do not have to fill in your details again when you leave another comment. These cookies will last for one year. If you visit our login page, we will set a temporary cookie to determine if your browser accepts cookies. This cookie contains no personal data and is discarded when you close your browser. When you log in, we will also set up several cookies to save your login information and your screen display choices. Login cookies last for two days, and screen options cookies last for a year. If you select \"Remember Me\", your login will persist for two weeks. If you log out of your account, the login cookies will be removed. If you edit or publish an article, an additional cookie will be saved in your browser. This cookie includes no personal data and simply indicates the post ID of the article you just edited. It expires after one day."}, "embeddedContent": {"title": "Embedded content from other websites", "content": "This site may contain embedded content (e.g. videos, images, articles, etc.). Embedded content from other websites behaves in the exact same way as if the visitor has visited the other website. These websites may collect data about you, use cookies, embed additional third-party tracking, and monitor your interaction with that embedded content, including tracking your interaction with the embedded content if you have an account and are logged in to that website."}, "dataSharing": {"title": "Who we share your data with", "content": "If you request a password reset, your IP address will be included in the reset email."}, "dataRetention": {"title": "How long we retain your data", "content": "If you leave a comment, the comment and its metadata are retained indefinitely. This is so we can recognize and approve any follow-up comments automatically instead of holding them in a moderation queue. For users that register on our website (if any), we also store the personal information they provide in their user profile. All users can see, edit, or delete their personal information at any time (except they cannot change their username). Website administrators can also view and edit that information."}}}, "register": {"title": "Create Your Account", "subtitle": "Join our community today.", "form": {"firstName": {"label": "First Name", "placeholder": "Enter your first name"}, "lastName": {"label": "Last Name", "placeholder": "Enter your last name"}, "email": {"label": "Email Address", "placeholder": "Enter your email address"}, "phone": {"label": "Phone Number", "placeholder": "Enter your phone number", "otpMessage": "OTP code will be sent to you via WhatsApp", "sendCodeButton": "Send Code", "sendingCode": "Sending Code...", "codeSent": "Code sent to your WhatsApp"}, "otp": {"label": "Verification Code", "placeholder": "Enter 6-digit code", "verifyButton": "Verify Code", "verifying": "Verifying...", "resendCode": "Resend Code", "resending": "Resending..."}, "password": {"label": "Password", "placeholder": "Create a strong password"}, "confirmPassword": {"label": "Confirm Password", "placeholder": "Confirm your password"}, "terms": {"label": "I agree to the", "termsLink": "Terms of Service", "and": "and", "privacyLink": "Privacy Policy"}, "submitButton": "Create Account", "submitting": "Creating Account..."}, "validation": {"firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "emailRequired": "Email address is required", "invalidEmail": "Please enter a valid email address", "phoneRequired": "Phone number is required", "invalidPhone": "Please enter a valid phone number", "phoneCountryCode": "Phone number must include country code (e.g., +**********)", "passwordRequired": "Password is required", "passwordTooShort": "Password must be at least 8 characters long", "passwordWeak": "Password must contain at least one uppercase letter, one lowercase letter, and one number", "confirmPasswordRequired": "Please confirm your password", "passwordsDoNotMatch": "Passwords do not match", "termsRequired": "You must agree to the Terms of Service and Privacy Policy", "otpRequired": "Verification code is required", "invalidOtp": "Please enter a valid 6-digit code", "otpIncorrect": "The code is wrong, please try again", "phoneNotVerified": "Please verify your phone number first"}, "footer": {"alreadyHaveAccount": "Already have an account?", "loginLink": "<PERSON><PERSON>"}}, "search": {"title": "Search Results", "searchPlaceholder": "Search for images, vectors, and more...", "searchButton": "Search", "filters": {"title": "Filters", "providers": "Providers", "fileType": "File Type", "all": "all", "items": "items"}, "fileTypes": {"photos": "photos", "vectors": "vectors", "illustrations": "illustrations", "icons": "icons", "templates": "templates"}, "pagination": {"previous": "Previous Items", "next": "Next Items", "page": "Page {{current}} of {{total}}"}, "suggestions": {"title": "Suggestions:"}, "actions": {"download": "Download", "simillars": "Show Similar Images", "favorite": "Add to favorites", "share": "Share"}, "toggleFilters": "Toggle filters", "imageDialog": {"creditRequired": "Credit required:", "addToCart": "Add to cart", "like": "Like", "viewFullImage": "View Full Image"}, "results": {"stockImages": "Stock images related to", "defaultQuery": "search", "description": "There is more than {{count}} stock images and vectors to {{query}}, enjoy!", "vectorsText": "vectors"}, "status": {"online": "Online", "offline": "Offline", "someProvidersOffline": "Some providers are currently offline", "offlineProvidersMessage": "The following providers are temporarily unavailable. Results from these sources may be limited."}}, "services": {"title": "Our Services", "subtitle": "Discover our comprehensive range of creative services designed to meet all your digital content needs", "viewService": "View Service", "learnMore": "Learn More", "breadcrumb": {"home": "Home", "services": "Services"}, "details": {"salesTime": "Sales Time", "subscriptionPeriod": "Subscription Period", "quantity": "Quantity", "contactUs": "Contact Us", "removeFromFavorites": "Remove from Favorites", "subscriptionOptions": {"1year": "1 Year", "2years": "2 Years"}, "pricing": {"originalPrice": "Original Price", "discountedPrice": "Discounted Price", "perYear": "per year", "save": "Save"}, "specifications": {"title": "Specifications", "features": "Features", "compatibility": "Compatibility", "support": "Support"}, "reviews": {"title": "Customer Reviews", "totalReviews": "Total Reviews", "averageRating": "Average Rating"}}, "items": {"youtube": {"title": "YouTube Premium", "description": "Access premium YouTube content with ad-free viewing, background play, and exclusive features for content creators.", "price": "179.00", "rating": "4.8", "salesTime": "Limited Time Offer", "totalReviews": "535", "features": ["Ad-free viewing experience", "Background play functionality", "YouTube Music Premium included", "Offline downloads", "Creator exclusive content"], "compatibility": "All devices and platforms", "support": "24/7 customer support"}, "adobe": {"title": "Adobe Creative Cloud", "description": "Complete suite of Adobe creative applications including Photoshop, Illustrator, After Effects, and more professional tools.", "price": "79.00", "rating": "4.9", "salesTime": "Best Value Package", "totalReviews": "1,247", "features": ["Full Adobe Creative Suite", "Cloud storage and sync", "Regular updates and new features", "Professional templates", "Collaboration tools"], "compatibility": "Windows, macOS, iOS, Android", "support": "Professional support team"}, "microsoft": {"title": "Microsoft Office 365", "description": "Professional Microsoft Office suite with Word, Excel, PowerPoint, Teams, and cloud storage solutions.", "price": "89.00", "rating": "4.7", "salesTime": "Business Essential", "totalReviews": "892", "features": ["Word, Excel, PowerPoint", "Microsoft Teams", "OneDrive cloud storage", "Outlook email", "Real-time collaboration"], "compatibility": "All major platforms", "support": "Enterprise support"}, "canva": {"title": "Canva Pro", "description": "Advanced design platform with premium templates, brand kit, background remover, and team collaboration features.", "price": "24.00", "rating": "4.6", "salesTime": "Popular Choice", "totalReviews": "2,156", "features": ["Premium templates library", "Brand kit and fonts", "Background remover", "Team collaboration", "Magic resize feature"], "compatibility": "Web, iOS, Android", "support": "Community and email support"}}}, "broadcast": {"title": "Broadcast", "description": "Send broadcast messages to users", "stats": {"totalUsers": {"title": "Total Users", "description": "Registered users"}, "messagesSentToday": {"title": "Sent Today", "description": "Messages Sent today"}}, "form": {"title": "Send New Broadcast Message", "description": "Send a message to all users or a specific group", "messageTitle": "Message Title", "messageContent": "Message Content", "targetAudience": "Target Audience", "allUsers": "All Users", "activeUsers": "Active Users Only", "premiumUsers": "Premium Users Only", "messageCategory": "Message Category", "textOnly": "Text Only", "textWithImage": "Text with Image", "uploadImage": "Upload Image", "imageUpload": {"title": "Upload Image for Message", "description": "Choose an image to attach with your message", "dragAndDrop": "Drag and drop image here", "orClickToBrowse": "or click to browse", "supportedFormats": "Supported formats: JPG, PNG, GIF (max 5MB)", "uploading": "Uploading...", "uploadSuccess": "Image uploaded successfully", "uploadError": "Failed to upload image", "removeImage": "Remove Image", "imagePreview": "Image Preview"}, "sending": "Sending...", "send": "Send Message", "placeholders": {"messageTitle": "Enter message title", "messageContent": "Write your message content here..."}, "validation": {"titleRequired": "Message title is required", "contentRequired": "Message content is required", "audienceRequired": "Please select target audience", "categoryRequired": "Please select message category"}}, "recentActivity": {"title": "Recent Activity", "description": "Latest sent messages and activities", "noActivity": "No recent activity", "noActivityDescription": "Sent messages will appear here", "showAll": "Show All", "allActivities": {"title": "All Activities", "description": "Complete history of all broadcast activities"}, "messageTypes": {"broadcast": "Broadcast Message", "notification": "Notification", "announcement": "Announcement", "alert": "<PERSON><PERSON>"}, "timeAgo": {"now": "Now", "minutesAgo": "{{count}}m ago", "minutesAgo_one": "1 minute ago", "minutesAgo_other": "{{count}} minutes ago", "hoursAgo": "{{count}}h ago", "hoursAgo_one": "1 hour ago", "hoursAgo_other": "{{count}} hours ago", "daysAgo": "{{count}}d ago", "daysAgo_one": "1 day ago", "daysAgo_other": "{{count}} days ago"}}, "toast": {"sendSuccess": "Message sent successfully", "sendError": "Failed to send message"}, "messagesLog": {"title": "Messages Log", "description": "Broadcast messages history", "table": {"headers": {"broadcastName": "Broadcast Name", "broadcastDetails": "Broadcast Details", "targetRecipients": "Target Recipients", "sentSuccessfully": "Sent Successfully", "failed": "Failed", "date": "Date", "status": "Status", "options": "Options"}, "status": {"completed": "Completed", "sending": "Sending"}, "actions": {"view": "View", "resend": "Resend", "delete": "Delete"}}, "empty": {"title": "No Messages", "description": "No broadcast messages have been sent yet"}}}, "common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "viewAll": "View All", "learnMore": "Learn More", "month": "Month", "download": "Download"}}